import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../data/models/app_model.dart';
import '../providers/checkpoint_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';

class CheckpointDialog extends ConsumerStatefulWidget {
  final AppModel app;

  const CheckpointDialog({
    super.key,
    required this.app,
  });

  @override
  ConsumerState<CheckpointDialog> createState() => _CheckpointDialogState();
}

class _CheckpointDialogState extends ConsumerState<CheckpointDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  DateTime _validFrom = DateTime.now();
  String _selectedColor = '#2196F3';

  final List<String> _colorOptions = [
    '#2196F3', // Blue
    '#4CAF50', // Green
    '#FF9800', // Orange
    '#F44336', // Red
    '#9C27B0', // Purple
    '#607D8B', // Blue Grey
    '#795548', // Brown
    '#FF5722', // Deep Orange
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  void _refreshCheckpointData() {
    invalidateCheckpointCache(ref, widget.app.id);

    ref.read(checkpointsForAppProvider(widget.app.id).notifier).refresh();
    ref.read(activeCheckpointForAppProvider(widget.app.id).notifier).refresh();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Dialog(
      child: ModernContainer(
        width: 800, // Increased width for better usability
        height: 700,
        child: ModernColumn(
          spacing: DesignTokens.spacingM,
          children: [
            // Header with padding
            Container(
              padding: const EdgeInsets.all(DesignTokens.spacingL),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: ModernColumn(
                spacing: DesignTokens.spacingM,
                children: [
                  _buildHeader(theme, isDark),
                  _buildTabBar(theme, isDark),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildCheckpointsTab(theme, isDark),
                  _buildCreateTab(theme, isDark),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDark) {
    return Row(
      children: [
        Icon(
          Icons.flag,
          color: isDark ? theme.colorScheme.primary : theme.colorScheme.primary,
          size: UIConstants.iconM,
        ),
        SizedBox(width: UIConstants.spacingS.w),
        Text(
          'Checkpoints for ${widget.app.name}',
          style: theme.textTheme.headlineSmall?.copyWith(
            color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: _refreshCheckpointData,
          icon: Icon(
            Icons.refresh,
            color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
          ),
          tooltip: 'Refresh checkpoints',
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.close,
            color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar(ThemeData theme, bool isDark) {
    return TabBar(
      controller: _tabController,
      indicatorColor: theme.colorScheme.primary,
      labelColor: theme.colorScheme.primary,
      unselectedLabelColor: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
      indicatorWeight: 3.0,
      indicatorSize: TabBarIndicatorSize.tab,
      labelStyle: theme.textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: theme.textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.w400,
      ),
      tabs: const [
        Tab(text: 'Manage'),
        Tab(text: 'Create New'),
      ],
    );
  }

  Widget _buildCheckpointsTab(ThemeData theme, bool isDark) {
    final checkpointsAsync = ref.watch(checkpointsForAppProvider(widget.app.id));

    return checkpointsAsync.when(
      data: (checkpoints) => _buildCheckpointsList(context, checkpoints, theme, isDark),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text(
          'Error loading checkpoints: $error',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
          ),
        ),
      ),
    );
  }

  Widget _buildCheckpointsList(BuildContext context, List<CheckpointWithStatus> checkpoints, ThemeData theme, bool isDark) {
    if (checkpoints.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.flag_outlined,
              size: 64.sp,
              color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray400,
            ),
            SizedBox(height: UIConstants.spacingM.h),
            Text(
              'No checkpoints yet',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            Text(
              'Create your first checkpoint in the "Create New" tab',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray500,
              ),
            ),
          ],
        ),
      );
    }

    return Consumer(
      builder: (context, ref, child) {
        final activeCheckpointState = ref.watch(activeCheckpointForAppProvider(widget.app.id));

        return ListView.builder(
          itemCount: checkpoints.length,
          itemBuilder: (context, index) {
            final checkpointWithStatus = checkpoints[index];

            // Compute active status from unified state management
            bool isLoading = activeCheckpointState.isLoading;
            bool isActive;

            // Use optimistic state if available, otherwise use server state
            if (activeCheckpointState.optimisticCheckpointId != null) {
              // We have optimistic state - use it for immediate feedback
              if (activeCheckpointState.optimisticCheckpointId == -1) {
                // Special case: optimistically clearing all checkpoints
                isActive = false;
              } else {
                // Normal case: optimistically activating a specific checkpoint
                isActive = activeCheckpointState.optimisticCheckpointId == checkpointWithStatus.checkpoint.id;
              }
            } else {
              // No optimistic state - use server state
              isActive = activeCheckpointState.activeCheckpoint?.id == checkpointWithStatus.checkpoint.id;
            }

            return Card(
              margin: EdgeInsets.only(bottom: UIConstants.spacingM.h),
              color: isDark ? theme.cardBg : theme.colorScheme.surface,
              elevation: 2,
              child: ListTile(
                leading: Container(
                  width: 24.w,
                  height: 24.h,
                  decoration: BoxDecoration(
                    color: Color(int.parse(checkpointWithStatus.color.replaceFirst('#', '0xFF'))),
                    shape: BoxShape.circle,
                  ),
                ),
                title: Text(
                  checkpointWithStatus.name,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                    fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (checkpointWithStatus.description != null)
                      Text(
                        checkpointWithStatus.description!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                        ),
                      ),
                    SizedBox(height: UIConstants.spacingXS.h),
                    Text(
                      'Valid from: ${checkpointWithStatus.validFrom.toString().split(' ')[0]}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray500,
                        fontSize: 10.sp,
                      ),
                    ),
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Active',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                            fontSize: 10.sp,
                          ),
                        ),
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            Switch(
                              value: isActive,
                              onChanged: isLoading ? null : (value) {
                                // SET STATE IMMEDIATELY - NO WAITING!
                                if (value) {
                                  // Activate this checkpoint IMMEDIATELY
                                  ref.read(activeCheckpointForAppProvider(widget.app.id).notifier).setActiveImmediate(checkpointWithStatus.checkpoint);
                                } else {
                                  // Deactivate this checkpoint IMMEDIATELY
                                  ref.read(activeCheckpointForAppProvider(widget.app.id).notifier).setInactiveImmediate();
                                }
                              },
                              activeColor: AppTheme.successColor,
                              inactiveThumbColor: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray400,
                              inactiveTrackColor: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                            ),
                            if (isLoading)
                              SizedBox(
                                width: 16.w,
                                height: 16.h,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(width: UIConstants.spacingS.w),
                    IconButton(
                      onPressed: () => _deleteCheckpoint(checkpointWithStatus.checkpoint),
                      icon: Icon(
                        Icons.delete_outline,
                        color: AppTheme.errorColor,
                        size: UIConstants.iconS,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildCreateTab(ThemeData theme, bool isDark) {
    return Padding(
      padding: EdgeInsets.all(UIConstants.spacingL.w),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Create New Checkpoint',
              style: theme.textTheme.titleMedium?.copyWith(
                color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: UIConstants.spacingL.h),
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'Checkpoint Name',
                hintText: 'e.g., Q1 2024, Project Alpha',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(UIConstants.radiusL),
                ),
                prefixIcon: Icon(Icons.flag),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a checkpoint name';
                }
                return null;
              },
            ),
            SizedBox(height: UIConstants.spacingM.h),
            TextFormField(
              controller: _descriptionController,
              decoration: InputDecoration(
                labelText: 'Description (Optional)',
                hintText: 'Add a description for this checkpoint',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(UIConstants.radiusL),
                ),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
            ),
            SizedBox(height: UIConstants.spacingM.h),
            Text(
              'Valid From',
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            InkWell(
              onTap: () => _selectDate(context),
              child: Container(
                padding: EdgeInsets.all(UIConstants.spacingM.w),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray300,
                  ),
                  borderRadius: BorderRadius.circular(UIConstants.radiusL),
                ),
                child: Row(
                  children: [
                    Icon(Icons.calendar_today),
                    SizedBox(width: UIConstants.spacingS.w),
                    Text(_formatDate(_validFrom)),
                  ],
                ),
              ),
            ),
            SizedBox(height: UIConstants.spacingM.h),
            Text(
              'Color',
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            Wrap(
              spacing: UIConstants.spacingS.w,
              children: _colorOptions.map((color) {
                final isSelected = color == _selectedColor;
                return GestureDetector(
                  onTap: () => setState(() => _selectedColor = color),
                  child: Container(
                    width: 32.w,
                    height: 32.h,
                    decoration: BoxDecoration(
                      color: Color(int.parse(color.replaceFirst('#', '0xFF'))),
                      shape: BoxShape.circle,
                      border: isSelected
                          ? Border.all(color: theme.primaryColor, width: 3)
                          : null,
                    ),
                    child: isSelected
                        ? Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16.sp,
                          )
                        : null,
                  ),
                );
              }).toList(),
            ),
            const Spacer(),
            Row(
              children: [
                Expanded(
                  child: OutlinedModernButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: DesignTokens.spacingM),
                Expanded(
                  child: PrimaryButton(
                    onPressed: _createCheckpoint,
                    child: const Text('Create Checkpoint'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _createCheckpoint() {
    if (_formKey.currentState?.validate() ?? false) {
      final name = _nameController.text.trim();

      // CREATE CHECKPOINT IMMEDIATELY - NO WAITING!
      ref.read(checkpointsForAppProvider(widget.app.id).notifier).createCheckpointImmediate(
        name,
        _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
        _validFrom,
        _selectedColor,
      );

      // Switch to "Manage" tab immediately
      _tabController.animateTo(0);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Checkpoint "$name" created successfully'),
          backgroundColor: AppTheme.successColor,
        ),
      );

      // Clear the form
      _nameController.clear();
      _descriptionController.clear();
      _selectedColor = '#2196F3';
      _validFrom = DateTime.now();
    }
  }

  void _selectDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: _validFrom,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() => _validFrom = date);
    }
  }

  void _deleteCheckpoint(CheckpointModel checkpoint) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Checkpoint'),
        content: Text('Are you sure you want to delete "${checkpoint.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              navigator.pop();

              try {
                await ref.read(checkpointsForAppProvider(widget.app.id).notifier).deleteCheckpoint(checkpoint.id);
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Checkpoint "${checkpoint.name}" deleted successfully'),
                      backgroundColor: AppTheme.successColor,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete checkpoint: $e'),
                      backgroundColor: AppTheme.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}