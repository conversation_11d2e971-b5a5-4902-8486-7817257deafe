import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/app_model.dart';
import '../../providers/checkpoint_providers.dart';
import '../../../app/design_system/design_system.dart';
import '../../../app/theme/app_theme.dart';

class CheckpointSelector extends ConsumerWidget {
  final AppModel app;
  final CheckpointModel? selectedCheckpoint;
  final ValueChanged<CheckpointModel?> onChanged;

  const CheckpointSelector({
    super.key,
    required this.app,
    required this.selectedCheckpoint,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final checkpointsAsync = ref.watch(checkpointsForAppProvider(app.id));
    final activeCheckpointState = ref.watch(activeCheckpointForAppProvider(app.id));

    return checkpointsAsync.when(
      data: (checkpoints) => _buildCheckpointSelector(checkpoints, activeCheckpointState, theme, isDark),
      loading: () => _buildLoadingSelector(theme, isDark),
      error: (error, stack) => _buildErrorSelector(theme, isDark),
    );
  }

  Widget _buildCheckpointSelector(List<CheckpointWithStatus> checkpoints, ActiveCheckpointState activeCheckpointState, ThemeData theme, bool isDark) {
    // Deduplicate checkpoints by ID to prevent dropdown errors
    final Map<int, CheckpointWithStatus> uniqueCheckpoints = {};
    for (final checkpoint in checkpoints) {
      uniqueCheckpoints[checkpoint.id] = checkpoint;
    }
    final deduplicatedCheckpoints = uniqueCheckpoints.values.toList();

    // Validate that selectedCheckpoint is in the available list
    CheckpointModel? validSelectedCheckpoint = selectedCheckpoint;
    if (selectedCheckpoint != null) {
      final isValidSelection = deduplicatedCheckpoints.any(
        (cws) => cws.checkpoint.id == selectedCheckpoint!.id
      );
      if (!isValidSelection) {
        validSelectedCheckpoint = null;
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: DesignTokens.spacingM),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray400,
          width: 1,
        ),
      ),
      child: DropdownButton<CheckpointModel?>(
        value: validSelectedCheckpoint,
        hint: Text(
          'All Checkpoints',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
          ),
        ),
        underline: const SizedBox(),
        isExpanded: true,
        dropdownColor: isDark ? theme.cardBg : theme.colorScheme.surface,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
        ),
        items: [
          DropdownMenuItem<CheckpointModel?>(
            value: null,
            child: Row(
              children: [
                Icon(
                  Icons.all_inclusive,
                  size: 16,
                  color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                ),
                const SizedBox(width: DesignTokens.spacingS),
                Text('All Checkpoints'),
              ],
            ),
          ),
          ...deduplicatedCheckpoints.map((checkpointWithStatus) {
            final checkpoint = checkpointWithStatus.checkpoint;

            // Use optimistic state for immediate UI feedback
            bool isActive = checkpointWithStatus.isActive;

            // Apply optimistic state if available (regardless of loading state for immediate feedback)
            if (activeCheckpointState.optimisticCheckpointId != null) {
              if (activeCheckpointState.optimisticCheckpointId == checkpoint.id) {
                // This checkpoint is optimistically active
                isActive = true;
              } else {
                // Another checkpoint is optimistically active, so this one is not
                isActive = false;
              }
            } else if (activeCheckpointState.isLoading && activeCheckpointState.optimisticCheckpointId == null) {
              // Optimistically clearing all checkpoints
              isActive = false;
            }

            return DropdownMenuItem<CheckpointModel?>(
              value: checkpoint,
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Color(int.parse(checkpoint.color.replaceFirst('#', '0xFF'))),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: DesignTokens.spacingS),
                  Expanded(
                    child: Text(
                      checkpoint.name,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
                  if (isActive)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: DesignTokens.spacingXS,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'ACTIVE',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.w600,
                          fontSize: 8,
                        ),
                      ),
                    ),
                ],
              ),
            );
          }),
        ],
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildLoadingSelector(ThemeData theme, bool isDark) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingM,
        vertical: DesignTokens.spacingM,
      ),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray400,
          width: 1,
        ),
      ),
      child: ModernRow(
        spacing: DesignTokens.spacingS,
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary500),
            ),
          ),
          Text(
            'Loading checkpoints...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorSelector(ThemeData theme, bool isDark) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingM,
        vertical: DesignTokens.spacingM,
      ),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray400,
          width: 1,
        ),
      ),
      child: ModernRow(
        spacing: DesignTokens.spacingS,
        children: [
          Icon(
            Icons.error_outline,
            size: 16,
            color: theme.colorScheme.error,
          ),
          Text(
            'Failed to load checkpoints',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ),
    );
  }
}