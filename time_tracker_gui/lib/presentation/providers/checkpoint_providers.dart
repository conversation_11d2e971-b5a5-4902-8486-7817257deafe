import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/app_model.dart';
import '../../data/repositories/time_tracker_repository.dart';
import 'app_providers.dart';

// Stream-based checkpoint providers that automatically update
final checkpointsStreamProvider = StreamProvider<List<CheckpointWithStatus>>((ref) {
  final repository = ref.watch(timeTrackerRepositoryProvider);
  // Use the new checkpointsWithStatusStream that includes active status
  return (repository as WebSocketTimeTrackerRepository).checkpointsWithStatusStream;
});

final activeCheckpointStreamProvider = StreamProvider<CheckpointModel?>((ref) {
  final repository = ref.watch(timeTrackerRepositoryProvider);
  return repository.activeCheckpointStream;
});

// App-specific checkpoint providers
final checkpointsForAppProvider = StateNotifierProvider.family<CheckpointsNotifier, AsyncValue<List<CheckpointWithStatus>>, int>((ref, appId) {
  final notifier = CheckpointsNotifier(ref.watch(timeTrackerRepositoryProvider), appId);

  // Listen to stream updates and update the notifier
  ref.listen(checkpointsStreamProvider, (previous, next) {
    next.whenData((checkpoints) {
      if (!notifier.mounted) return;
      // Filter checkpoints for this specific app
      final appCheckpoints = checkpoints.where((c) => c.checkpoint.appId == appId).toList();
      notifier.updateFromStream(appCheckpoints);
    });
  });

  return notifier;
});

// Helper method to invalidate checkpoint cache
void invalidateCheckpointCache(WidgetRef ref, int appId) {
  ref.invalidate(checkpointsForAppProvider(appId));
  ref.invalidate(activeCheckpointForAppProvider(appId));
}

class ActiveCheckpointState {
  final CheckpointModel? activeCheckpoint;
  final bool isLoading;
  final int? optimisticCheckpointId; // For immediate UI feedback

  const ActiveCheckpointState({
    this.activeCheckpoint,
    this.isLoading = false,
    this.optimisticCheckpointId,
  });

  ActiveCheckpointState copyWith({
    CheckpointModel? activeCheckpoint,
    bool? isLoading,
    int? optimisticCheckpointId,
  }) {
    return ActiveCheckpointState(
      activeCheckpoint: activeCheckpoint ?? this.activeCheckpoint,
      isLoading: isLoading ?? this.isLoading,
      optimisticCheckpointId: optimisticCheckpointId,
    );
  }
}

final activeCheckpointForAppProvider = StateNotifierProvider.family<ActiveCheckpointNotifier, ActiveCheckpointState, int>((ref, appId) {
  final notifier = ActiveCheckpointNotifier(ref.watch(timeTrackerRepositoryProvider), appId);

  // Listen to stream updates and update the notifier
  ref.listen(activeCheckpointStreamProvider, (previous, next) {
    next.whenData((activeCheckpoint) {
      if (!notifier.mounted) return;
      // Only update if the active checkpoint belongs to this app
      if (activeCheckpoint?.appId == appId || activeCheckpoint == null) {
        notifier.updateFromStream(activeCheckpoint);
      }
    });
  });

  return notifier;
});

final selectedCheckpointFilterProvider = StateProvider<CheckpointModel?>((ref) => null);

final checkpointStatsProvider = FutureProvider.family<List<CheckpointDurationModel>, int>((ref, checkpointId) async {
  final repository = ref.watch(timeTrackerRepositoryProvider);
  return await repository.getCheckpointStats(checkpointId);
});

class CheckpointsNotifier extends StateNotifier<AsyncValue<List<CheckpointWithStatus>>> {
  final TimeTrackerRepository _repository;
  final int _appId;

  CheckpointsNotifier(this._repository, this._appId) : super(const AsyncValue.loading()) {
    loadCheckpoints();
  }

  Future<void> loadCheckpoints() async {
    try {
      if (!mounted) return;
      state = const AsyncValue.loading();
      final checkpoints = await _repository.getCheckpoints(_appId);
      // Convert to CheckpointWithStatus - active status will be updated by WebSocket
      final checkpointsWithStatus = checkpoints.map((checkpoint) => CheckpointWithStatus(
        checkpoint: checkpoint,
        isActive: false, // This will be updated by the actual WebSocket response
      )).toList();
      if (mounted) {
        state = AsyncValue.data(checkpointsWithStatus);
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> refresh() async {
    // Force a fresh request to the server
    await loadCheckpoints();
  }

  void updateFromStream(List<CheckpointWithStatus> checkpoints) {
    if (mounted) {
      state = AsyncValue.data(checkpoints);
    }
  }

  Future<void> createCheckpoint(String name, String? description, DateTime validFrom, String color) async {
    try {
      // Add optimistic checkpoint immediately
      final optimisticCheckpoint = CheckpointWithStatus(
        checkpoint: CheckpointModel(
          id: -DateTime.now().millisecondsSinceEpoch, // Negative temporary ID
          name: name,
          description: description,
          createdAt: DateTime.now(),
          validFrom: validFrom,
          color: color,
          appId: _appId,
        ),
        isActive: false,
      );

      // Add to current state immediately
      state.whenData((currentCheckpoints) {
        if (mounted) {
          state = AsyncValue.data([...currentCheckpoints, optimisticCheckpoint]);
        }
      });

      await _repository.createCheckpoint(name, description, validFrom, color, _appId);
      // Don't call loadCheckpoints here - let the stream update handle it
    } catch (error, stackTrace) {
      print('Error creating checkpoint: $error');
      print('Stack trace: $stackTrace');
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
      // Re-throw to allow UI to handle the error
      rethrow;
    }
  }

  Future<void> deleteCheckpoint(int checkpointId) async {
    try {
      // Remove checkpoint optimistically
      state.whenData((currentCheckpoints) {
        if (mounted) {
          final updatedCheckpoints = currentCheckpoints.where((c) => c.checkpoint.id != checkpointId).toList();
          state = AsyncValue.data(updatedCheckpoints);
        }
      });

      await _repository.deleteCheckpoint(checkpointId, _appId);
      // Don't call loadCheckpoints here - let the stream update handle it
    } catch (error, stackTrace) {
      print('Error deleting checkpoint: $error');
      print('Stack trace: $stackTrace');
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
      // Re-throw to allow UI to handle the error
      rethrow;
    }
  }
}

class ActiveCheckpointNotifier extends StateNotifier<ActiveCheckpointState> {
  final TimeTrackerRepository _repository;
  final int _appId;

  ActiveCheckpointNotifier(this._repository, this._appId) : super(const ActiveCheckpointState()) {
    loadActiveCheckpoint();
  }

  Future<void> loadActiveCheckpoint() async {
    try {
      state = state.copyWith(isLoading: true);
      final activeCheckpoint = await _repository.getActiveCheckpoint(_appId);
      if (mounted) {
        state = ActiveCheckpointState(
          activeCheckpoint: activeCheckpoint,
          isLoading: false,
        );
      }
    } catch (error) {
      if (mounted) {
        state = const ActiveCheckpointState(isLoading: false);
      }
    }
  }

  Future<void> refresh() async {
    // Force a fresh request to the server
    await loadActiveCheckpoint();
  }

  void updateFromStream(CheckpointModel? activeCheckpoint) {
    if (mounted) {
      // Clear optimistic state when stream update arrives
      state = ActiveCheckpointState(
        activeCheckpoint: activeCheckpoint,
        isLoading: false,
        optimisticCheckpointId: null,
      );
    }
  }

  Future<void> setActive(CheckpointModel checkpoint) async {
    try {
      // Set optimistic state for immediate UI feedback
      state = state.copyWith(
        optimisticCheckpointId: checkpoint.id,
        isLoading: true,
      );

      await _repository.setActiveCheckpoint(checkpoint.id, _appId);

      // The stream will update the actual state, so we just clear loading
      if (mounted) {
        state = state.copyWith(isLoading: false);
      }
    } catch (error, stackTrace) {
      print('Error setting active checkpoint: $error');
      print('Stack trace: $stackTrace');

      // Revert optimistic state on error
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          optimisticCheckpointId: null,
        );
      }

      // Re-throw to allow UI to handle the error
      rethrow;
    }
  }

  Future<void> setInactive(CheckpointModel checkpoint) async {
    try {
      // Set optimistic state for immediate UI feedback
      state = state.copyWith(
        optimisticCheckpointId: null, // Clear active checkpoint
        isLoading: true,
      );

      // Clear the active checkpoint instead of setting it
      await _repository.clearActiveCheckpoint(_appId);

      // The stream will update the actual state, so we just clear loading
      if (mounted) {
        state = state.copyWith(isLoading: false);
      }
    } catch (error, stackTrace) {
      print('Error clearing active checkpoint: $error');
      print('Stack trace: $stackTrace');

      // Revert optimistic state on error
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          optimisticCheckpointId: null,
        );
      }

      // Re-throw to allow UI to handle the error
      rethrow;
    }
  }

  Future<void> clearActive() async {
    try {
      // Set optimistic state for immediate UI feedback
      state = state.copyWith(
        optimisticCheckpointId: -1, // Special value for clearing
        isLoading: true,
      );

      await _repository.clearActiveCheckpoint(_appId);

      // The stream will update the actual state, so we just clear loading
      if (mounted) {
        state = state.copyWith(isLoading: false);
      }
    } catch (error) {
      // Revert optimistic state on error
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          optimisticCheckpointId: null,
        );
      }
    }
  }
}