import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/app_model.dart';
import '../../data/repositories/time_tracker_repository.dart';
import 'app_providers.dart';

// Stream-based checkpoint providers that automatically update
final checkpointsStreamProvider = StreamProvider<List<CheckpointWithStatus>>((ref) {
  final repository = ref.watch(timeTrackerRepositoryProvider);
  // Use the new checkpointsWithStatusStream that includes active status
  return (repository as WebSocketTimeTrackerRepository).checkpointsWithStatusStream;
});

final activeCheckpointStreamProvider = StreamProvider<CheckpointModel?>((ref) {
  final repository = ref.watch(timeTrackerRepositoryProvider);
  return repository.activeCheckpointStream;
});

// App-specific checkpoint providers
final checkpointsForAppProvider = StateNotifierProvider.family<CheckpointsNotifier, AsyncValue<List<CheckpointWithStatus>>, int>((ref, appId) {
  final notifier = CheckpointsNotifier(ref.watch(timeTrackerRepositoryProvider), appId);

  // Listen to stream updates and update the notifier
  ref.listen(checkpointsStreamProvider, (previous, next) {
    next.whenData((checkpoints) {
      if (!notifier.mounted) return;
      // Filter checkpoints for this specific app
      final appCheckpoints = checkpoints.where((c) => c.checkpoint.appId == appId).toList();
      notifier.updateFromStream(appCheckpoints);
    });
  });

  return notifier;
});

// Helper method to invalidate checkpoint cache
void invalidateCheckpointCache(WidgetRef ref, int appId) {
  ref.invalidate(checkpointsForAppProvider(appId));
  ref.invalidate(activeCheckpointForAppProvider(appId));
}

class ActiveCheckpointState {
  final CheckpointModel? activeCheckpoint;
  final bool isLoading;
  final int? optimisticCheckpointId; // For immediate UI feedback

  const ActiveCheckpointState({
    this.activeCheckpoint,
    this.isLoading = false,
    this.optimisticCheckpointId,
  });

  ActiveCheckpointState copyWith({
    CheckpointModel? activeCheckpoint,
    bool? isLoading,
    int? optimisticCheckpointId,
  }) {
    return ActiveCheckpointState(
      activeCheckpoint: activeCheckpoint ?? this.activeCheckpoint,
      isLoading: isLoading ?? this.isLoading,
      optimisticCheckpointId: optimisticCheckpointId,
    );
  }
}

final activeCheckpointForAppProvider = StateNotifierProvider.family<ActiveCheckpointNotifier, ActiveCheckpointState, int>((ref, appId) {
  final notifier = ActiveCheckpointNotifier(ref.watch(timeTrackerRepositoryProvider), appId);

  // Listen to stream updates and update the notifier
  ref.listen(activeCheckpointStreamProvider, (previous, next) {
    next.whenData((activeCheckpoint) {
      if (!notifier.mounted) return;
      // Only update if the active checkpoint belongs to this app
      if (activeCheckpoint?.appId == appId || activeCheckpoint == null) {
        notifier.updateFromStream(activeCheckpoint);
      }
    });
  });

  return notifier;
});

final selectedCheckpointFilterProvider = StateProvider<CheckpointModel?>((ref) => null);

final checkpointStatsProvider = FutureProvider.family<List<CheckpointDurationModel>, int>((ref, checkpointId) async {
  final repository = ref.watch(timeTrackerRepositoryProvider);
  return await repository.getCheckpointStats(checkpointId);
});

class CheckpointsNotifier extends StateNotifier<AsyncValue<List<CheckpointWithStatus>>> {
  final TimeTrackerRepository _repository;
  final int _appId;

  CheckpointsNotifier(this._repository, this._appId) : super(const AsyncValue.loading()) {
    loadCheckpoints();
  }

  Future<void> loadCheckpoints() async {
    try {
      if (!mounted) return;
      state = const AsyncValue.loading();
      final checkpoints = await _repository.getCheckpoints(_appId);
      // Convert to CheckpointWithStatus - active status will be updated by WebSocket
      final checkpointsWithStatus = checkpoints.map((checkpoint) => CheckpointWithStatus(
        checkpoint: checkpoint,
        isActive: false, // This will be updated by the actual WebSocket response
      )).toList();
      if (mounted) {
        state = AsyncValue.data(checkpointsWithStatus);
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> refresh() async {
    // Force a fresh request to the server
    await loadCheckpoints();
  }

  void updateFromStream(List<CheckpointWithStatus> checkpoints) {
    if (mounted) {
      // Merge stream data with any optimistic checkpoints (negative IDs)
      state.whenData((currentCheckpoints) {
        final optimisticCheckpoints = currentCheckpoints.where((c) => c.checkpoint.id < 0).toList();
        final serverCheckpoints = checkpoints;

        // Combine optimistic and server checkpoints
        final mergedCheckpoints = [...serverCheckpoints, ...optimisticCheckpoints];
        state = AsyncValue.data(mergedCheckpoints);
      });

      // If no current state, just use server data
      if (state is! AsyncData) {
        state = AsyncValue.data(checkpoints);
      }
    }
  }

  Future<void> createCheckpoint(String name, String? description, DateTime validFrom, String color) async {
    try {
      // Add optimistic checkpoint immediately
      final optimisticCheckpoint = CheckpointWithStatus(
        checkpoint: CheckpointModel(
          id: -DateTime.now().millisecondsSinceEpoch, // Negative temporary ID
          name: name,
          description: description,
          createdAt: DateTime.now(),
          validFrom: validFrom,
          color: color,
          appId: _appId,
        ),
        isActive: false,
      );

      // Add to current state immediately
      state.whenData((currentCheckpoints) {
        if (mounted) {
          state = AsyncValue.data([...currentCheckpoints, optimisticCheckpoint]);
        }
      });

      await _repository.createCheckpoint(name, description, validFrom, color, _appId);
      // Don't call loadCheckpoints here - let the stream update handle it
    } catch (error, stackTrace) {
      print('Error creating checkpoint: $error');
      print('Stack trace: $stackTrace');
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
      // Re-throw to allow UI to handle the error
      rethrow;
    }
  }

  Future<void> deleteCheckpoint(int checkpointId) async {
    try {
      // Remove checkpoint optimistically
      state.whenData((currentCheckpoints) {
        if (mounted) {
          final updatedCheckpoints = currentCheckpoints.where((c) => c.checkpoint.id != checkpointId).toList();
          state = AsyncValue.data(updatedCheckpoints);
        }
      });

      // Only send to server if it's a real checkpoint (positive ID)
      if (checkpointId > 0) {
        await _repository.deleteCheckpoint(checkpointId, _appId);
      }
      // Don't call loadCheckpoints here - let the stream update handle it
    } catch (error, stackTrace) {
      print('Error deleting checkpoint: $error');
      print('Stack trace: $stackTrace');
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
      // Re-throw to allow UI to handle the error
      rethrow;
    }
  }

  // IMMEDIATE CREATION - NO WAITING FOR SERVER!
  void createCheckpointImmediate(String name, String? description, DateTime validFrom, String color) {
    // CREATE CHECKPOINT IMMEDIATELY with temporary ID
    final tempId = -DateTime.now().millisecondsSinceEpoch;
    final newCheckpoint = CheckpointWithStatus(
      checkpoint: CheckpointModel(
        id: tempId,
        name: name,
        description: description,
        createdAt: DateTime.now(),
        validFrom: validFrom,
        color: color,
        appId: _appId,
      ),
      isActive: false,
    );

    // Add to current state immediately
    state.whenData((currentCheckpoints) {
      if (mounted) {
        state = AsyncValue.data([...currentCheckpoints, newCheckpoint]);
      }
    });

    // Send to server in background - don't wait
    _repository.createCheckpoint(name, description, validFrom, color, _appId).catchError((error) {
      // If error, remove the temp checkpoint
      state.whenData((currentCheckpoints) {
        if (mounted) {
          final updatedCheckpoints = currentCheckpoints.where((c) => c.checkpoint.id != tempId).toList();
          state = AsyncValue.data(updatedCheckpoints);
        }
      });
    });
  }
}

class ActiveCheckpointNotifier extends StateNotifier<ActiveCheckpointState> {
  final TimeTrackerRepository _repository;
  final int _appId;

  ActiveCheckpointNotifier(this._repository, this._appId) : super(const ActiveCheckpointState()) {
    loadActiveCheckpoint();
  }

  Future<void> loadActiveCheckpoint() async {
    try {
      state = state.copyWith(isLoading: true);
      final activeCheckpoint = await _repository.getActiveCheckpoint(_appId);
      if (mounted) {
        state = ActiveCheckpointState(
          activeCheckpoint: activeCheckpoint,
          isLoading: false,
        );
      }
    } catch (error) {
      if (mounted) {
        state = const ActiveCheckpointState(isLoading: false);
      }
    }
  }

  Future<void> refresh() async {
    // Force a fresh request to the server
    await loadActiveCheckpoint();
  }

  void updateFromStream(CheckpointModel? activeCheckpoint) {
    if (mounted) {
      // Smart optimistic state management: only clear if server state matches expectation
      int? newOptimisticId = state.optimisticCheckpointId;

      if (state.optimisticCheckpointId != null) {
        // Check if server state matches our optimistic expectation
        if (state.optimisticCheckpointId == activeCheckpoint?.id) {
          // Server confirmed our optimistic activation - clear optimistic state
          newOptimisticId = null;
        } else if (state.optimisticCheckpointId == -1 && activeCheckpoint == null) {
          // Server confirmed our optimistic clearing - clear optimistic state
          newOptimisticId = null;
        }
        // Otherwise keep optimistic state until server catches up
      }

      state = ActiveCheckpointState(
        activeCheckpoint: activeCheckpoint,
        isLoading: false,
        optimisticCheckpointId: newOptimisticId,
      );
    }
  }

  Future<void> setActive(CheckpointModel checkpoint) async {
    try {
      // Set optimistic state for immediate UI feedback
      state = state.copyWith(
        optimisticCheckpointId: checkpoint.id,
        isLoading: true,
      );

      await _repository.setActiveCheckpoint(checkpoint.id, _appId);

      // The stream will update the actual state, so we just clear loading
      if (mounted) {
        state = state.copyWith(isLoading: false);
      }
    } catch (error, stackTrace) {
      print('Error setting active checkpoint: $error');
      print('Stack trace: $stackTrace');

      // Revert optimistic state on error
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          optimisticCheckpointId: null,
        );
      }

      // Re-throw to allow UI to handle the error
      rethrow;
    }
  }

  Future<void> setInactive(CheckpointModel checkpoint) async {
    try {
      // Set optimistic state for immediate UI feedback
      state = state.copyWith(
        optimisticCheckpointId: -1, // Special value for clearing active checkpoint
        isLoading: true,
      );

      // Clear the active checkpoint instead of setting it
      await _repository.clearActiveCheckpoint(_appId);

      // The stream will update the actual state, so we just clear loading
      if (mounted) {
        state = state.copyWith(isLoading: false);
      }
    } catch (error, stackTrace) {
      print('Error clearing active checkpoint: $error');
      print('Stack trace: $stackTrace');

      // Revert optimistic state on error
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          optimisticCheckpointId: null,
        );
      }

      // Re-throw to allow UI to handle the error
      rethrow;
    }
  }

  Future<void> clearActive() async {
    try {
      // Set optimistic state for immediate UI feedback
      state = state.copyWith(
        optimisticCheckpointId: -1, // Special value for clearing
        isLoading: true,
      );

      await _repository.clearActiveCheckpoint(_appId);

      // The stream will update the actual state, so we just clear loading
      if (mounted) {
        state = state.copyWith(isLoading: false);
      }
    } catch (error) {
      // Revert optimistic state on error
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          optimisticCheckpointId: null,
        );
      }
    }
  }

  // IMMEDIATE METHODS - NO WAITING FOR SERVER!
  void setActiveImmediate(CheckpointModel checkpoint) {
    // SET STATE IMMEDIATELY - only if it's a real checkpoint (positive ID)
    if (checkpoint.id > 0) {
      state = ActiveCheckpointState(
        activeCheckpoint: checkpoint,
        isLoading: false,
        optimisticCheckpointId: checkpoint.id,
      );

      // Send to server in background - don't wait
      _repository.setActiveCheckpoint(checkpoint.id, _appId).catchError((error) {
        // If error, revert state
        if (mounted) {
          state = state.copyWith(
            activeCheckpoint: null,
            optimisticCheckpointId: null,
          );
        }
      });
    }
  }

  void setInactiveImmediate() {
    // SET STATE IMMEDIATELY
    state = const ActiveCheckpointState(
      activeCheckpoint: null,
      isLoading: false,
      optimisticCheckpointId: -1,
    );

    // Send to server in background - don't wait
    _repository.clearActiveCheckpoint(_appId).catchError((error) {
      // If error, could revert state but for now just ignore
    });
  }
}